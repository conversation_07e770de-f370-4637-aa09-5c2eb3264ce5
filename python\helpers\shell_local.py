import select
import subprocess
import time
import sys
import threading
import queue
from typing import Optional, Tuple

class LocalInteractiveSession:
    def __init__(self):
        self.process = None
        self.full_output = ''

    async def connect(self):
        # Start a new subprocess with the appropriate shell for the OS
        if sys.platform.startswith('win'):
            # Windows - use PowerShell with UTF-8 encoding
            import os
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            self.process = subprocess.Popen(
                ['powershell.exe', '-Command', '-'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                bufsize=1,
                env=env
            )
        else:
            # macOS and Linux
            self.process = subprocess.Popen(
                ['/bin/bash'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                bufsize=1
            )

    def close(self):
        if self.process:
            self.process.terminate()
            self.process.wait()

    def send_command(self, command: str):
        if not self.process:
            raise Exception("Shell not connected")
        self.full_output = ""
        self.process.stdin.write(command + '\n') # type: ignore
        self.process.stdin.flush() # type: ignore
 
    async def read_output(self, timeout: float = 0, reset_full_output: bool = False) -> Tuple[str, Optional[str]]:
        if not self.process:
            raise Exception("Shell not connected")

        if reset_full_output:
            self.full_output = ""

        # Simple approach: just wait for process to complete and read all output
        if sys.platform.startswith('win'):
            # Windows: wait for process completion and read all output
            try:
                # Wait for process to complete with timeout
                if timeout > 0:
                    self.process.wait(timeout=timeout)
                else:
                    self.process.wait(timeout=30)  # Default 30 second timeout

                # Read all output
                stdout_output = self.process.stdout.read() if self.process.stdout else ""  # type: ignore
                stderr_output = self.process.stderr.read() if self.process.stderr else ""  # type: ignore

                output = stdout_output + stderr_output
                if output:
                    self.full_output += output
                    return self.full_output, output
                else:
                    return self.full_output, None

            except subprocess.TimeoutExpired:
                # Process didn't complete in time, try to read partial output
                try:
                    # Try to read available output without blocking
                    stdout_output = ""
                    stderr_output = ""

                    # Read stdout if available
                    if self.process.stdout:
                        try:
                            stdout_output = self.process.stdout.read()  # type: ignore
                        except:
                            pass

                    # Read stderr if available
                    if self.process.stderr:
                        try:
                            stderr_output = self.process.stderr.read()  # type: ignore
                        except:
                            pass

                    output = stdout_output + stderr_output
                    if output:
                        self.full_output += output
                        return self.full_output, output
                    else:
                        return self.full_output, None

                except Exception:
                    return self.full_output, None

            except Exception:
                return self.full_output, None
        else:
            # Unix/Linux: use select as before
            partial_output = ''
            start_time = time.time()

            while (timeout <= 0 or time.time() - start_time < timeout):
                rlist, _, _ = select.select([self.process.stdout], [], [], 0.1)
                if rlist:
                    line = self.process.stdout.readline()  # type: ignore
                    if line:
                        partial_output += line
                        self.full_output += line
                        time.sleep(0.1)
                    else:
                        break  # No more output
                else:
                    break  # No data available

            if not partial_output:
                return self.full_output, None

            return self.full_output, partial_output