# Ignore common unwanted files globally
**/.DS_Store
**/.env
**/__pycache__/
**/.conda/

# Ignore docker/run/agent-zero directory
docker/run/agent-zero/

#Ignore cursor rules
.cursor/

# ignore test files in root dir
/*.test.py

# Ignore git internal files (for bundler)
.git/

# Ignore all contents of the virtual environment directory
.venv/

# Handle bundle directory
bundle/*/
!bundle/mac_pkg_scripts

# Handle work_dir directory
work_dir/*

# Handle specific docker directories
docker/run/agent-zero/**

# Handle memory directory
memory/**
!memory/**/

# Handle logs directory
logs/*

# Handle tmp directory
tmp/*

# Handle knowledge directory
knowledge/**
!knowledge/**/
# Explicitly allow the default folder in knowledge
!knowledge/default/
!knowledge/default/**

# Handle instruments directory
instruments/**
!instruments/**/
# Explicitly allow the default folder in instruments
!instruments/default/
!instruments/default/**

# Global rule to include .gitkeep files anywhere
!**/.gitkeep
agent_history.gif